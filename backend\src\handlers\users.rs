use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use chrono::Utc;
use sea_orm::{ActiveModelTrait, EntityTrait, Set};
use uuid::Uuid;

use crate::{
    database::DbConn,
    entities::{user, User},
    models::ApiResponse,
};

/// 创建用户
pub async fn create_user(
    State(db): State<DbConn>,
    Json(payload): Json<user::CreateUser>,
) -> Result<Json<ApiResponse<user::Model>>, StatusCode> {
    let now = Utc::now();

    let new_user = user::ActiveModel {
        id: Set(Uuid::new_v4()),
        username: Set(payload.username),
        email: Set(payload.email),
        created_at: Set(now),
        updated_at: Set(now),
    };

    let user = new_user
        .insert(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(<PERSON><PERSON>(ApiResponse::success(user)))
}

/// 获取所有用户
pub async fn get_users(
    State(db): State<DbConn>,
) -> Result<Json<ApiResponse<Vec<user::Model>>>, StatusCode> {
    let users = User::find()
        .all(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(ApiResponse::success(users)))
}

/// 获取单个用户
pub async fn get_user(
    State(db): State<DbConn>,
    Path(user_id): Path<Uuid>,
) -> Result<Json<ApiResponse<user::Model>>, StatusCode> {
    let user = User::find_by_id(user_id)
        .one(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match user {
        Some(user) => Ok(Json(ApiResponse::success(user))),
        None => Err(StatusCode::NOT_FOUND),
    }
}

/// 删除用户
pub async fn delete_user(
    State(db): State<DbConn>,
    Path(user_id): Path<Uuid>,
) -> Result<StatusCode, StatusCode> {
    let result = User::delete_by_id(user_id)
        .exec(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    if result.rows_affected > 0 {
        Ok(StatusCode::NO_CONTENT)
    } else {
        Err(StatusCode::NOT_FOUND)
    }
}
