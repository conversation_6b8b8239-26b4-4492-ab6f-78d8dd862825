# PostgreSQL 配置指南

## 环境变量配置

请在项目根目录创建 `.env` 文件，包含以下配置：

```env
# PostgreSQL 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/reminder_db

# 服务器配置  
PORT=3000

# 日志级别
RUST_LOG=info

# 应用配置
APP_NAME=Reminder Backend
APP_VERSION=0.1.0
```

## PostgreSQL 安装和设置

### 1. 安装 PostgreSQL

**Windows (推荐使用 PostgreSQL 官方安装程序):**
- 下载：https://www.postgresql.org/download/windows/
- 安装时记住设置的密码

**使用 Docker (跨平台):**
```bash
docker run --name postgres-reminder \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=reminder_db \
  -p 5432:5432 \
  -d postgres:15
```

### 2. 创建数据库

连接到 PostgreSQL 并创建数据库：

```sql
-- 连接到 PostgreSQL
psql -U postgres -h localhost

-- 创建数据库
CREATE DATABASE reminder_db;

-- 创建用户（可选）
CREATE USER reminder_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE reminder_db TO reminder_user;
```

### 3. 配置连接字符串

根据你的设置，更新 `.env` 文件中的 `DATABASE_URL`：

```env
# 使用 postgres 用户
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/reminder_db

# 或使用自定义用户
DATABASE_URL=postgresql://reminder_user:your_password@localhost:5432/reminder_db
```

## 验证连接

运行项目验证 PostgreSQL 连接：

```bash
cargo run
```

如果连接成功，你会看到类似的日志：
```
2025-08-28T03:37:32.123Z INFO backend: 连接数据库: localhost:5432/reminder_db
2025-08-28T03:37:32.456Z INFO backend: 数据库连接成功，开始运行迁移...
2025-08-28T03:37:32.789Z INFO backend: 数据库迁移完成
2025-08-28T03:37:32.890Z INFO backend: 🚀 服务器启动在 0.0.0.0:3000
```

## 常见问题

### 1. 连接被拒绝
- 确保 PostgreSQL 服务正在运行
- 检查端口 5432 是否被占用
- 验证用户名和密码是否正确

### 2. 数据库不存在
- 确保已创建数据库 `reminder_db`
- 检查数据库名称拼写是否正确

### 3. 权限问题
- 确保用户有访问数据库的权限
- 检查 `pg_hba.conf` 配置（如果使用自定义配置）
