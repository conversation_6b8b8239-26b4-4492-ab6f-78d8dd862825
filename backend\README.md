# 到期提醒工具 - 后台服务

一个基于Rust开发的到期提醒工具后台服务，支持定期提醒（如吃药）和到期时间提醒（如食品保质期）。

## 功能特性

### 📋 核心功能
- **用户管理**：支持多用户系统
- **分类管理**：对提醒项进行分类管理
- **提醒管理**：创建、编辑、删除、查看提醒
- **定时调度**：自动检查并发送到期提醒

### 🔔 提醒类型
1. **一次性提醒**：如食品到期、药品过期等
2. **定期提醒**：如每日吃药、每周体检等

### ⏰ 重复模式
- 每日 (Daily)
- 每周 (Weekly) 
- 每月 (Monthly)
- 每年 (Yearly)
- 自定义 (Custom) - 支持cron表达式

## 技术栈

- **Web框架**：Axum
- **数据库**：SQLite (支持扩展到PostgreSQL)
- **定时任务**：tokio-cron-scheduler
- **异步运行时**：Tokio
- **序列化**：Serde
- **日志**：Tracing

## 项目结构

```
backend/
├── src/
│   ├── main.rs           # 主程序入口
│   ├── models.rs         # 数据模型定义
│   ├── database.rs       # 数据库操作
│   ├── scheduler.rs      # 定时调度器
│   └── handlers/         # API处理器
│       ├── mod.rs
│       ├── users.rs      # 用户相关API
│       ├── categories.rs # 分类相关API
│       └── reminders.rs  # 提醒相关API
├── Cargo.toml           # 项目依赖
└── README.md           # 项目说明
```

## 数据模型

### 用户 (Users)
```rust
struct User {
    id: Uuid,
    username: String,
    email: String,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}
```

### 分类 (Categories) 
```rust
struct Category {
    id: Uuid,
    name: String,
    description: Option<String>,
    user_id: Uuid,
    created_at: DateTime<Utc>,
}
```

### 提醒 (Reminders)
```rust
struct Reminder {
    id: Uuid,
    title: String,
    description: Option<String>,
    reminder_type: ReminderType,  // OneTime | Recurring
    user_id: Uuid,
    category_id: Option<Uuid>,
    remind_at: DateTime<Utc>,
    expire_at: Option<DateTime<Utc>>,
    recurrence_pattern: Option<RecurrencePattern>,
    cron_expression: Option<String>,
    is_active: bool,
    is_completed: bool,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}
```

## API 接口

### 用户管理
- `POST /api/users` - 创建用户
- `GET /api/users` - 获取所有用户
- `GET /api/users/{user_id}` - 获取单个用户
- `DELETE /api/users/{user_id}` - 删除用户

### 分类管理
- `POST /api/users/{user_id}/categories` - 创建分类
- `GET /api/users/{user_id}/categories` - 获取用户分类
- `GET /api/users/{user_id}/categories/{category_id}` - 获取单个分类
- `DELETE /api/users/{user_id}/categories/{category_id}` - 删除分类

### 提醒管理
- `POST /api/users/{user_id}/reminders` - 创建提醒
- `GET /api/users/{user_id}/reminders` - 获取用户提醒
- `GET /api/users/{user_id}/reminders/{reminder_id}` - 获取单个提醒
- `PUT /api/users/{user_id}/reminders/{reminder_id}` - 更新提醒
- `DELETE /api/users/{user_id}/reminders/{reminder_id}` - 删除提醒

## 快速开始

### 环境要求
- Rust 1.70+
- SQLite

### 安装运行

1. **克隆项目**
```bash
git clone <repository>
cd backend
```

2. **设置环境变量** (可选)
```bash
# 创建 .env 文件
echo "DATABASE_URL=sqlite:reminder.db" > .env
echo "PORT=3000" >> .env
echo "RUST_LOG=info" >> .env
```

3. **运行项目**
```bash
cargo run
```

服务器将在 `http://localhost:3000` 启动。

### 使用示例

#### 1. 创建用户
```bash
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "张三",
    "email": "<EMAIL>"
  }'
```

#### 2. 创建分类
```bash
curl -X POST http://localhost:3000/api/users/{user_id}/categories \
  -H "Content-Type: application/json" \
  -d '{
    "name": "医疗健康",
    "description": "医疗相关的提醒"
  }'
```

#### 3. 创建定期提醒 (每日吃药)
```bash
curl -X POST http://localhost:3000/api/users/{user_id}/reminders \
  -H "Content-Type: application/json" \
  -d '{
    "title": "早餐后服药",
    "description": "每天早餐后服用降压药",
    "reminder_type": "Recurring",
    "remind_at": "2024-01-01T08:00:00Z",
    "recurrence_pattern": "Daily"
  }'
```

#### 4. 创建一次性提醒 (食品到期)
```bash
curl -X POST http://localhost:3000/api/users/{user_id}/reminders \
  -H "Content-Type: application/json" \
  -d '{
    "title": "牛奶即将过期",
    "description": "冰箱里的牛奶将在明天过期",
    "reminder_type": "OneTime",
    "remind_at": "2024-01-15T18:00:00Z",
    "expire_at": "2024-01-16T00:00:00Z"
  }'
```

## 调度系统

系统使用 `tokio-cron-scheduler` 实现定时调度：

- **检查频率**：每分钟检查一次到期提醒
- **处理逻辑**：
  - 一次性提醒：发送通知后标记为完成
  - 定期提醒：发送通知后计算下次提醒时间
- **日志记录**：所有提醒触发都会记录日志

## 扩展功能

### 通知方式
目前支持控制台日志输出，可扩展为：
- 📧 邮件通知
- 📱 短信通知
- 🔔 推送通知
- 🔗 Webhook通知

### 高级调度
- 支持自定义cron表达式
- 支持时区处理
- 支持节假日跳过

## 开发计划

- [ ] 邮件通知集成
- [ ] 短信通知集成
- [ ] Web前端界面
- [ ] 移动端应用
- [ ] 用户认证系统
- [ ] 数据导入导出
- [ ] 提醒模板功能

## 许可证

MIT License
