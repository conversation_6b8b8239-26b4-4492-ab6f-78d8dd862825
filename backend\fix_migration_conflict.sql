-- PostgreSQL 迁移冲突修复脚本
-- 此脚本用于解决 SeaORM 迁移表的主键冲突问题

-- 1. 查看当前迁移表状态
SELECT * FROM seaql_migrations;

-- 2. 如果需要清理冲突记录，删除现有的迁移记录
-- 注意：这会导致重新运行所有迁移
-- DELETE FROM seaql_migrations WHERE version = 'migrator';

-- 3. 或者删除整个迁移表重新开始
-- DROP TABLE IF EXISTS seaql_migrations;

-- 4. 查看当前数据库中的所有表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public';

-- 5. 如果需要完全重置数据库，删除所有应用表
-- 注意：这会删除所有数据！
-- DROP TABLE IF EXISTS reminder_logs CASCADE;
-- DROP TABLE IF EXISTS reminders CASCADE;
-- DROP TABLE IF EXISTS categories CASCADE;
-- DROP TABLE IF EXISTS users CASCADE;
-- DROP TABLE IF EXISTS seaql_migrations CASCADE;
