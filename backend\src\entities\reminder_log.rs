use sea_orm::entity::prelude::*;
use sea_orm::Set;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "reminder_logs")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: Uuid,
    pub reminder_id: Uuid,
    pub triggered_at: ChronoDateTimeUtc,
    pub status: String,
    pub message: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::reminder::Entity",
        from = "Column::ReminderId",
        to = "super::reminder::Column::Id"
    )]
    Reminder,
}

impl Related<super::reminder::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Reminder.def()
    }
}

impl ActiveModelBehavior for ActiveModel {
    fn new() -> Self {
        Self {
            id: Set(Uuid::new_v4()),
            triggered_at: Set(chrono::Utc::now()),
            ..ActiveModelTrait::default()
        }
    }
}
