# 到期提醒工具 - 快速测试指南

## 🚀 快速开始

### 1. 启动服务
```bash
cd backend
cargo run
```
服务将在 `http://localhost:3000` 启动

### 2. 导入Apifox
- 打开Apifox
- 导入 `apifox_collection.json` 文件
- 设置环境变量：baseUrl = `http://localhost:3000`

---

## 📋 完整测试流程

### 步骤1：创建用户
**POST** `/api/users`
```json
{
  "username": "张三",
  "email": "<EMAIL>"
}
```
**记录返回的 `user_id`**

### 步骤2：创建分类
**POST** `/api/users/{user_id}/categories`
```json
{
  "name": "医疗健康",
  "description": "医疗相关的提醒"
}
```
**记录返回的 `category_id`**

### 步骤3：创建立即测试提醒（验证调度器）
**POST** `/api/users/{user_id}/reminders`
```json
{
  "title": "立即测试提醒",
  "description": "用于测试调度器功能",
  "reminder_type": "OneTime",
  "category_id": "{category_id}",
  "remind_at": "2025-08-27T11:40:00Z",
  "expire_at": null,
  "recurrence_pattern": null,
  "cron_expression": null
}
```
> 注意：将 `remind_at` 设置为当前时间后1-2分钟，观察控制台日志

### 步骤4：创建每日吃药提醒
**POST** `/api/users/{user_id}/reminders`
```json
{
  "title": "早餐后服药",
  "description": "每天早餐后服用降压药",
  "reminder_type": "Recurring",
  "category_id": "{category_id}",
  "remind_at": "2025-08-28T08:00:00Z",
  "expire_at": null,
  "recurrence_pattern": "Daily",
  "cron_expression": null
}
```

### 步骤5：创建食品到期提醒
**POST** `/api/users/{user_id}/reminders`
```json
{
  "title": "牛奶即将过期",
  "description": "冰箱里的牛奶将在明天过期",
  "reminder_type": "OneTime",
  "category_id": "{category_id}",
  "remind_at": "2025-08-28T18:00:00Z",
  "expire_at": "2025-08-29T00:00:00Z",
  "recurrence_pattern": null,
  "cron_expression": null
}
```

### 步骤6：查看所有提醒
**GET** `/api/users/{user_id}/reminders`

### 步骤7：筛选激活的提醒
**GET** `/api/users/{user_id}/reminders?is_active=true`

### 步骤8：更新提醒状态
**PUT** `/api/users/{user_id}/reminders/{reminder_id}`
```json
{
  "is_completed": true
}
```

---

## 🎯 重点测试场景

### 1. 调度器功能测试
- 创建1分钟后的立即提醒
- 观察控制台输出：`🔔 提醒通知: 立即测试提醒`
- 验证一次性提醒被标记为完成

### 2. 不同提醒类型测试
- **OneTime**: 一次性提醒（食品到期、药品过期等）
- **Recurring**: 定期提醒（每日吃药、每周体检等）

### 3. 重复模式测试
- `Daily`: 每天重复
- `Weekly`: 每周重复  
- `Monthly`: 每月重复
- `Yearly`: 每年重复

### 4. 查询筛选测试
- 按分类筛选：`?category_id={category_id}`
- 按状态筛选：`?is_active=true`
- 按类型筛选：`?reminder_type=Recurring`

---

## 📱 使用场景示例

### 🏥 医疗健康场景
```json
{
  "title": "服用维生素D",
  "description": "每天早上服用维生素D补充剂",
  "reminder_type": "Recurring",
  "recurrence_pattern": "Daily",
  "remind_at": "2025-08-28T08:00:00Z"
}
```

### 🍎 食品管理场景
```json
{
  "title": "酸奶即将过期",
  "description": "冰箱里的酸奶3天后过期",
  "reminder_type": "OneTime",
  "remind_at": "2025-08-30T20:00:00Z",
  "expire_at": "2025-08-31T23:59:59Z"
}
```

### 🔧 生活提醒场景
```json
{
  "title": "更换空气净化器滤芯",
  "description": "每3个月更换一次HEPA滤芯",
  "reminder_type": "Recurring",
  "recurrence_pattern": "Monthly",
  "remind_at": "2025-11-27T10:00:00Z"
}
```

---

## 🔍 调试和监控

### 1. 查看服务日志
观察控制台输出：
- 服务启动信息
- 数据库连接状态
- 调度器启动信息
- 提醒触发日志

### 2. 数据库检查
SQLite数据库文件：`reminder.db`
```bash
# 查看数据库内容（需要安装sqlite3）
sqlite3 reminder.db
.tables
SELECT * FROM reminders;
```

### 3. 常见问题排查
- **端口占用**：确保3000端口未被占用
- **时间格式**：必须使用ISO 8601格式，如 `2025-08-27T08:00:00Z`
- **UUID格式**：确保使用正确的UUID格式

---

## 📊 API响应状态码

| 状态码 | 说明 | 场景 |
|--------|------|------|
| 200 | 成功 | GET请求成功 |
| 201 | 创建成功 | POST请求成功 |
| 204 | 无内容 | DELETE请求成功 |
| 400 | 请求错误 | 参数格式错误 |
| 404 | 未找到 | 资源不存在 |
| 500 | 服务器错误 | 内部错误 |

---

## 🎉 验证清单

- [ ] 服务成功启动
- [ ] 创建用户成功
- [ ] 创建分类成功
- [ ] 创建立即提醒，观察到调度器触发
- [ ] 创建定期提醒成功
- [ ] 创建一次性提醒成功
- [ ] 查询提醒列表正常
- [ ] 筛选功能正常
- [ ] 更新提醒状态成功
- [ ] 删除提醒成功

完成以上验证后，说明到期提醒工具后台功能正常运行！🚀
