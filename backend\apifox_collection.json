{"info": {"name": "到期提醒工具 API", "description": "基于Rust+SeaORM的到期提醒工具API集合", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "userId", "value": "550e8400-e29b-41d4-a716-446655440000", "type": "string"}, {"key": "categoryId", "value": "660e8400-e29b-41d4-a716-446655440001", "type": "string"}], "item": [{"name": "用户管理", "item": [{"name": "创建用户", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"张三\",\n  \"email\": \"zhang<PERSON>@example.com\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}, {"name": "获取所有用户", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}, {"name": "获取单个用户", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}, {"name": "删除用户", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}]}, {"name": "分类管理", "item": [{"name": "创建分类", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"医疗健康\",\n  \"description\": \"医疗相关的提醒\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/categories", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "categories"]}}}, {"name": "获取用户分类", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/categories", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "categories"]}}}, {"name": "获取单个分类", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/categories/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "categories", "{{categoryId}}"]}}}, {"name": "删除分类", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/categories/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "categories", "{{categoryId}}"]}}}]}, {"name": "提醒管理", "item": [{"name": "创建定期提醒（每日吃药）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"早餐后服药\",\n  \"description\": \"每天早餐后服用降压药\",\n  \"reminder_type\": \"Recurring\",\n  \"category_id\": \"{{categoryId}}\",\n  \"remind_at\": \"2025-08-27T08:00:00Z\",\n  \"expire_at\": null,\n  \"recurrence_pattern\": \"Daily\",\n  \"cron_expression\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders"]}}}, {"name": "创建一次性提醒（食品到期）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"牛奶即将过期\",\n  \"description\": \"冰箱里的牛奶将在明天过期\",\n  \"reminder_type\": \"OneTime\",\n  \"category_id\": \"{{categoryId}}\",\n  \"remind_at\": \"2025-08-28T18:00:00Z\",\n  \"expire_at\": \"2025-08-29T00:00:00Z\",\n  \"recurrence_pattern\": null,\n  \"cron_expression\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders"]}}}, {"name": "创建立即提醒（测试调度器）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"立即测试提醒\",\n  \"description\": \"用于测试调度器功能的立即提醒\",\n  \"reminder_type\": \"OneTime\",\n  \"category_id\": \"{{categoryId}}\",\n  \"remind_at\": \"2025-08-27T03:27:00Z\",\n  \"expire_at\": null,\n  \"recurrence_pattern\": null,\n  \"cron_expression\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders"]}}}, {"name": "获取用户提醒", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders"]}}}, {"name": "获取激活的提醒", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders?is_active=true", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders"], "query": [{"key": "is_active", "value": "true"}]}}}, {"name": "获取定期提醒", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders?reminder_type=Recurring", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders"], "query": [{"key": "reminder_type", "value": "Recurring"}]}}}, {"name": "获取单个提醒", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders/{{reminderId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders", "{{reminderId}}"]}}}, {"name": "更新提醒", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"晚餐后服药\",\n  \"description\": \"改为晚餐后服用\",\n  \"remind_at\": \"2025-08-27T19:00:00Z\",\n  \"is_active\": true,\n  \"is_completed\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders/{{reminderId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders", "{{reminderId}}"]}}}, {"name": "停用提醒", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"is_active\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders/{{reminderId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders", "{{reminderId}}"]}}}, {"name": "完成提醒", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"is_completed\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders/{{reminderId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders", "{{reminderId}}"]}}}, {"name": "删除提醒", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/reminders/{{reminderId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "reminders", "{{reminderId}}"]}}}]}]}