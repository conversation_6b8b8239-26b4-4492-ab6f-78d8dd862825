use chrono::{DateTime, Utc};
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use tokio_cron_scheduler::{Job, JobScheduler};
use uuid::Uuid;

use crate::{
    database::DbConn,
    entities::{reminder, reminder_log, Reminder},
};

pub struct ReminderScheduler {
    scheduler: JobScheduler,
    db: DbConn,
}

impl ReminderScheduler {
    pub async fn new(db: DbConn) -> Result<Self, Box<dyn std::error::Error>> {
        let scheduler = JobScheduler::new().await?;
        
        Ok(Self { scheduler, db })
    }

    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.scheduler.start().await?;
        
        // 添加主检查任务 - 每分钟检查一次到期的提醒
        let db = self.db.clone();
        let job = Job::new_async("0 * * * * *", move |_uuid, _l| {
            let db = db.clone();
            Box::pin(async move {
                if let Err(e) = check_due_reminders(db).await {
                    tracing::error!("检查到期提醒时出错: {:?}", e);
                }
            })
        })?;
        
        self.scheduler.add(job).await?;
        
        tracing::info!("提醒调度器已启动");
        Ok(())
    }

    pub async fn stop(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        self.scheduler.shutdown().await?;
        tracing::info!("提醒调度器已停止");
        Ok(())
    }
}

/// 检查到期的提醒
async fn check_due_reminders(db: DbConn) -> Result<(), sea_orm::DbErr> {
    let now = Utc::now();
    
    // 查找所有活跃且到期的提醒
    let due_reminders = Reminder::find()
        .filter(reminder::Column::IsActive.eq(true))
        .filter(reminder::Column::IsCompleted.eq(false))
        .filter(reminder::Column::RemindAt.lte(now))
        .all(&db)
        .await?;

    for reminder in due_reminders {
        if let Err(e) = process_reminder(&db, &reminder).await {
            let error_msg = format!("{:?}", e);
            tracing::error!("处理提醒 {} 时出错: {}", reminder.id, error_msg);
            
            // 记录失败日志
            if let Err(log_err) = log_reminder_trigger(&db, reminder.id, "failed", Some(error_msg)).await {
                tracing::error!("记录提醒日志失败: {:?}", log_err);
            }
        }
    }

    Ok(())
}

/// 处理单个提醒
async fn process_reminder(db: &DbConn, reminder: &reminder::Model) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 发送提醒通知（这里可以扩展为邮件、短信、推送等）
    send_notification(reminder).await?;
    
    // 记录成功日志
    log_reminder_trigger(db, reminder.id, "success", None).await?;
    
    // 根据提醒类型处理后续逻辑
    match reminder.reminder_type {
        reminder::ReminderType::OneTime => {
            // 一次性提醒 - 标记为完成
            let mut active_reminder: reminder::ActiveModel = reminder.clone().into();
            active_reminder.is_completed = Set(true);
            active_reminder.updated_at = Set(Utc::now());
            active_reminder.update(db).await?;
            
            tracing::info!("一次性提醒 {} 已完成", reminder.title);
        }
        reminder::ReminderType::Recurring => {
            // 定期提醒 - 计算下次提醒时间
            if let Some(next_remind_time) = calculate_next_reminder_time(reminder) {
                let mut active_reminder: reminder::ActiveModel = reminder.clone().into();
                active_reminder.remind_at = Set(next_remind_time);
                active_reminder.updated_at = Set(Utc::now());
                active_reminder.update(db).await?;
                
                tracing::info!("定期提醒 {} 已更新，下次提醒时间: {}", reminder.title, next_remind_time);
            } else {
                tracing::warn!("无法计算定期提醒 {} 的下次时间", reminder.title);
            }
        }
    }
    
    Ok(())
}

/// 发送通知
async fn send_notification(reminder: &reminder::Model) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 这里实现具体的通知逻辑
    // 可以是控制台输出、邮件、短信、推送通知等
    
    tracing::info!("🔔 提醒通知: {}", reminder.title);
    if let Some(description) = &reminder.description {
        tracing::info!("📝 详情: {}", description);
    }
    
    // TODO: 实现实际的通知发送逻辑
    // - 邮件通知
    // - 短信通知  
    // - 推送通知
    // - Webhook通知
    
    Ok(())
}

/// 计算下次提醒时间
fn calculate_next_reminder_time(reminder: &reminder::Model) -> Option<DateTime<Utc>> {
    use chrono::Duration;
    
    let current_time = reminder.remind_at;
    
    match &reminder.recurrence_pattern {
        Some(reminder::RecurrencePattern::Daily) => {
            Some(current_time + Duration::days(1))
        }
        Some(reminder::RecurrencePattern::Weekly) => {
            Some(current_time + Duration::weeks(1))
        }
        Some(reminder::RecurrencePattern::Monthly) => {
            // 简单实现：加30天，实际应该考虑月份的天数
            Some(current_time + Duration::days(30))
        }
        Some(reminder::RecurrencePattern::Yearly) => {
            Some(current_time + Duration::days(365))
        }
        Some(reminder::RecurrencePattern::Custom) => {
            // TODO: 实现cron表达式解析
            // 这需要额外的cron解析库
            tracing::warn!("自定义cron表达式暂未实现");
            None
        }
        None => None,
    }
}

/// 记录提醒触发日志
async fn log_reminder_trigger(
    db: &DbConn,
    reminder_id: Uuid,
    status: &str,
    message: Option<String>,
) -> Result<(), sea_orm::DbErr> {
    let new_log = reminder_log::ActiveModel {
        id: Set(Uuid::new_v4()),
        reminder_id: Set(reminder_id),
        triggered_at: Set(Utc::now()),
        status: Set(status.to_string()),
        message: Set(message),
    };
    
    new_log.insert(db).await?;
    
    Ok(())
}
