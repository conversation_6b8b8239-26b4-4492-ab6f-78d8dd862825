use sea_orm::entity::prelude::*;
use sea_orm::Set;
use serde::{Deserialize, Serialize};

/// 提醒类型
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Text")]
pub enum ReminderType {
    #[sea_orm(string_value = "OneTime")]
    OneTime,
    #[sea_orm(string_value = "Recurring")]
    Recurring,
}

/// 重复频率
#[derive(Debug, Clone, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "String", db_type = "Text")]
pub enum RecurrencePattern {
    #[sea_orm(string_value = "Daily")]
    Daily,
    #[sea_orm(string_value = "Weekly")]
    Weekly,
    #[sea_orm(string_value = "Monthly")]
    Monthly,
    #[sea_orm(string_value = "Yearly")]
    Yearly,
    #[sea_orm(string_value = "Custom")]
    Custom,
}

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "reminders")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub reminder_type: ReminderType,
    pub user_id: Uuid,
    pub category_id: Option<Uuid>,
    
    // 时间相关
    pub remind_at: ChronoDateTimeUtc,
    pub expire_at: Option<ChronoDateTimeUtc>,
    
    // 重复相关
    pub recurrence_pattern: Option<RecurrencePattern>,
    pub cron_expression: Option<String>,
    
    // 状态
    pub is_active: bool,
    pub is_completed: bool,
    
    // 元数据
    pub created_at: ChronoDateTimeUtc,
    pub updated_at: ChronoDateTimeUtc,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::UserId",
        to = "super::user::Column::Id"
    )]
    User,
    #[sea_orm(
        belongs_to = "super::category::Entity",
        from = "Column::CategoryId",
        to = "super::category::Column::Id"
    )]
    Category,
    #[sea_orm(has_many = "super::reminder_log::Entity")]
    ReminderLogs,
}

impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl Related<super::category::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Category.def()
    }
}

impl Related<super::reminder_log::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::ReminderLogs.def()
    }
}

impl ActiveModelBehavior for ActiveModel {
    fn new() -> Self {
        Self {
            id: Set(Uuid::new_v4()),
            is_active: Set(true),
            is_completed: Set(false),
            created_at: Set(chrono::Utc::now()),
            updated_at: Set(chrono::Utc::now()),
            ..ActiveModelTrait::default()
        }
    }

    fn before_save<'life0, 'async_trait, C>(
        mut self,
        _db: &'life0 C,
        _insert: bool,
    ) -> ::core::pin::Pin<Box<dyn ::core::future::Future<Output = Result<Self, DbErr>> + ::core::marker::Send + 'async_trait>>
    where
        'life0: 'async_trait,
        Self: 'async_trait,
        C: 'async_trait + ConnectionTrait,
    {
        Box::pin(async move {
            self.updated_at = Set(chrono::Utc::now());
            Ok(self)
        })
    }
}

// DTO for creating reminder
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateReminder {
    pub title: String,
    pub description: Option<String>,
    pub reminder_type: ReminderType,
    pub category_id: Option<Uuid>,
    pub remind_at: ChronoDateTimeUtc,
    pub expire_at: Option<ChronoDateTimeUtc>,
    pub recurrence_pattern: Option<RecurrencePattern>,
    pub cron_expression: Option<String>,
}

// DTO for updating reminder
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateReminder {
    pub title: Option<String>,
    pub description: Option<String>,
    pub remind_at: Option<ChronoDateTimeUtc>,
    pub expire_at: Option<ChronoDateTimeUtc>,
    pub recurrence_pattern: Option<RecurrencePattern>,
    pub cron_expression: Option<String>,
    pub is_active: Option<bool>,
    pub is_completed: Option<bool>,
}
