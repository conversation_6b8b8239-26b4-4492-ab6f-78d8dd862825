use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use chrono::Utc;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use uuid::Uuid;

use crate::{
    database::DbConn,
    entities::{category, Category},
    models::ApiResponse,
};

/// 创建分类
pub async fn create_category(
    State(db): State<DbConn>,
    Path(user_id): Path<Uuid>,
    Json(payload): Json<category::CreateCategory>,
) -> Result<Json<ApiResponse<category::Model>>, StatusCode> {
    let now = Utc::now();

    let new_category = category::ActiveModel {
        id: Set(Uuid::new_v4()),
        name: Set(payload.name),
        description: Set(payload.description),
        user_id: Set(user_id),
        created_at: Set(now),
    };

    let category = new_category
        .insert(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(ApiResponse::success(category)))
}

/// 获取用户的所有分类
pub async fn get_categories(
    State(db): State<DbConn>,
    Path(user_id): Path<Uuid>,
) -> Result<Json<ApiResponse<Vec<category::Model>>>, StatusCode> {
    let categories = Category::find()
        .filter(category::Column::UserId.eq(user_id))
        .all(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(ApiResponse::success(categories)))
}

/// 获取单个分类
pub async fn get_category(
    State(db): State<DbConn>,
    Path((user_id, category_id)): Path<(Uuid, Uuid)>,
) -> Result<Json<ApiResponse<category::Model>>, StatusCode> {
    let category = Category::find()
        .filter(category::Column::Id.eq(category_id))
        .filter(category::Column::UserId.eq(user_id))
        .one(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match category {
        Some(category) => Ok(Json(ApiResponse::success(category))),
        None => Err(StatusCode::NOT_FOUND),
    }
}

/// 删除分类
pub async fn delete_category(
    State(db): State<DbConn>,
    Path((user_id, category_id)): Path<(Uuid, Uuid)>,
) -> Result<StatusCode, StatusCode> {
    let result = Category::delete_many()
        .filter(category::Column::Id.eq(category_id))
        .filter(category::Column::UserId.eq(user_id))
        .exec(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    if result.rows_affected > 0 {
        Ok(StatusCode::NO_CONTENT)
    } else {
        Err(StatusCode::NOT_FOUND)
    }
}
