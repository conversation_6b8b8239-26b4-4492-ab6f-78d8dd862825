[package]
name = "backend"
version = "0.1.0"
edition = "2024"
default-run = "backend"

[[bin]]
name = "backend"
path = "src/main.rs"

[[bin]]
name = "test_pg_connection"
path = "test_pg_connection.rs"

[[bin]]
name = "fix_migration"
path = "fix_migration.rs"

[dependencies]
# Web框架
axum = "0.8"
tokio = { version = "1.0", features = ["full"] }
tower = "0.5"
tower-http = { version = "0.5", features = ["cors"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 数据库 - SeaORM with PostgreSQL
sea-orm = { version = "1", features = ["sqlx-postgres", "runtime-tokio-rustls", "macros", "with-chrono", "with-uuid"] }
sea-orm-migration = "1"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# 定时任务
tokio-cron-scheduler = "0.9"

# 环境变量
dotenvy = "0.15"

# 日志
tracing = "0.1"
tracing-subscriber = "0.3"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"
