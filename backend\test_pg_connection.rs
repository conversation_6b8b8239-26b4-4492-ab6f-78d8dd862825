// PostgreSQL 连接测试脚本
// 运行方式: cargo run --bin test_pg_connection

use sea_orm::{Database, DatabaseConnection, DbErr};
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载环境变量
    dotenvy::dotenv().ok();
    
    // 从环境变量获取数据库连接字符串
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| {
            println!("警告: DATABASE_URL 环境变量未设置，使用默认值");
            "postgresql://postgres:password@localhost:5432/reminder_db".to_string()
        });
    
    println!("测试 PostgreSQL 连接...");
    println!("连接字符串: {}", database_url.split('@').last().unwrap_or("隐藏连接信息"));
    
    // 尝试连接数据库
    match Database::connect(&database_url).await {
        Ok(db) => {
            println!("✅ PostgreSQL 连接成功!");
            
            // 测试一个简单的查询
            match test_database_query(&db).await {
                Ok(_) => println!("✅ 数据库查询测试成功!"),
                Err(e) => println!("❌ 数据库查询测试失败: {}", e),
            }
        }
        Err(e) => {
            println!("❌ PostgreSQL 连接失败: {}", e);
            println!("\n📋 解决步骤:");
            println!("1. 确保 PostgreSQL 服务正在运行");
            println!("2. 检查 .env 文件中的 DATABASE_URL 配置");
            println!("3. 确保数据库 'reminder_db' 已创建");
            println!("4. 验证用户名和密码是否正确");
            return Err(e.into());
        }
    }
    
    Ok(())
}

async fn test_database_query(db: &DatabaseConnection) -> Result<(), DbErr> {
    use sea_orm::*;
    
    // 执行一个简单的查询来测试连接
    let result = db
        .execute(Statement::from_string(
            db.get_database_backend(),
            "SELECT 1 as test_value".to_string(),
        ))
        .await?;
    
    println!("查询结果: {:?}", result);
    Ok(())
}
