use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub created_at: ChronoDateTimeUtc,
    pub updated_at: ChronoDateTimeUtc,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::category::Entity")]
    Categories,
    #[sea_orm(has_many = "super::reminder::Entity")]
    Reminders,
}

impl Related<super::category::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Categories.def()
    }
}

impl Related<super::reminder::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Reminders.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// DTO for creating user
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateUser {
    pub username: String,
    pub email: String,
}
