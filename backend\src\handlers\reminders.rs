use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use chrono::Utc;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use serde::Deserialize;
use uuid::Uuid;

use crate::{
    database::DbConn,
    entities::{reminder, Reminder},
    models::ApiResponse,
};

#[derive(Deserialize)]
pub struct ReminderQuery {
    pub category_id: Option<Uuid>,
    pub is_active: Option<bool>,
    pub reminder_type: Option<String>,
}

/// 创建提醒
pub async fn create_reminder(
    State(db): State<DbConn>,
    Path(user_id): Path<Uuid>,
    Json(payload): Json<reminder::CreateReminder>,
) -> Result<Json<ApiResponse<reminder::Model>>, StatusCode> {
    let now = Utc::now();

    let new_reminder = reminder::ActiveModel {
        id: Set(Uuid::new_v4()),
        title: Set(payload.title),
        description: Set(payload.description),
        reminder_type: Set(payload.reminder_type),
        user_id: Set(user_id),
        category_id: Set(payload.category_id),
        remind_at: Set(payload.remind_at),
        expire_at: Set(payload.expire_at),
        recurrence_pattern: Set(payload.recurrence_pattern),
        cron_expression: Set(payload.cron_expression),
        is_active: Set(true),
        is_completed: Set(false),
        created_at: Set(now),
        updated_at: Set(now),
    };

    let reminder = new_reminder
        .insert(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(ApiResponse::success(reminder)))
}

/// 获取用户的所有提醒
pub async fn get_reminders(
    State(db): State<DbConn>,
    Path(user_id): Path<Uuid>,
    Query(query): Query<ReminderQuery>,
) -> Result<Json<ApiResponse<Vec<reminder::Model>>>, StatusCode> {
    let mut find = Reminder::find().filter(reminder::Column::UserId.eq(user_id));

    if let Some(category_id) = query.category_id {
        find = find.filter(reminder::Column::CategoryId.eq(category_id));
    }

    if let Some(is_active) = query.is_active {
        find = find.filter(reminder::Column::IsActive.eq(is_active));
    }

    if let Some(reminder_type) = query.reminder_type {
        if reminder_type == "OneTime" {
            find = find.filter(reminder::Column::ReminderType.eq(reminder::ReminderType::OneTime));
        } else if reminder_type == "Recurring" {
            find = find.filter(reminder::Column::ReminderType.eq(reminder::ReminderType::Recurring));
        }
    }

    let reminders = find
        .all(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(ApiResponse::success(reminders)))
}

/// 获取单个提醒
pub async fn get_reminder(
    State(db): State<DbConn>,
    Path((user_id, reminder_id)): Path<(Uuid, Uuid)>,
) -> Result<Json<ApiResponse<reminder::Model>>, StatusCode> {
    let reminder = Reminder::find()
        .filter(reminder::Column::Id.eq(reminder_id))
        .filter(reminder::Column::UserId.eq(user_id))
        .one(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match reminder {
        Some(reminder) => Ok(Json(ApiResponse::success(reminder))),
        None => Err(StatusCode::NOT_FOUND),
    }
}

/// 更新提醒
pub async fn update_reminder(
    State(db): State<DbConn>,
    Path((user_id, reminder_id)): Path<(Uuid, Uuid)>,
    Json(payload): Json<reminder::UpdateReminder>,
) -> Result<Json<ApiResponse<reminder::Model>>, StatusCode> {
    let now = Utc::now();

    // 首先查找要更新的提醒
    let existing_reminder = Reminder::find()
        .filter(reminder::Column::Id.eq(reminder_id))
        .filter(reminder::Column::UserId.eq(user_id))
        .one(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let reminder = match existing_reminder {
        Some(reminder) => reminder,
        None => return Err(StatusCode::NOT_FOUND),
    };

    // 构建更新模型
    let mut active_reminder: reminder::ActiveModel = reminder.into();
    
    if let Some(title) = payload.title {
        active_reminder.title = Set(title);
    }
    if let Some(description) = payload.description {
        active_reminder.description = Set(Some(description));
    }
    if let Some(remind_at) = payload.remind_at {
        active_reminder.remind_at = Set(remind_at);
    }
    if let Some(expire_at) = payload.expire_at {
        active_reminder.expire_at = Set(Some(expire_at));
    }
    if let Some(recurrence_pattern) = payload.recurrence_pattern {
        active_reminder.recurrence_pattern = Set(Some(recurrence_pattern));
    }
    if let Some(cron_expression) = payload.cron_expression {
        active_reminder.cron_expression = Set(Some(cron_expression));
    }
    if let Some(is_active) = payload.is_active {
        active_reminder.is_active = Set(is_active);
    }
    if let Some(is_completed) = payload.is_completed {
        active_reminder.is_completed = Set(is_completed);
    }
    
    active_reminder.updated_at = Set(now);

    let updated_reminder = active_reminder
        .update(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(ApiResponse::success(updated_reminder)))
}

/// 删除提醒
pub async fn delete_reminder(
    State(db): State<DbConn>,
    Path((user_id, reminder_id)): Path<(Uuid, Uuid)>,
) -> Result<StatusCode, StatusCode> {
    let result = Reminder::delete_many()
        .filter(reminder::Column::Id.eq(reminder_id))
        .filter(reminder::Column::UserId.eq(user_id))
        .exec(&db)
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    if result.rows_affected > 0 {
        Ok(StatusCode::NO_CONTENT)
    } else {
        Err(StatusCode::NOT_FOUND)
    }
}