use sea_orm_migration::prelude::*;

pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20220101_000001_create_users_table::Migration),
            Box::new(m20220101_000002_create_categories_table::Migration),
            Box::new(m20220101_000003_create_reminders_table::Migration),
            Box::new(m20220101_000004_create_reminder_logs_table::Migration),
        ]
    }
}

mod m20220101_000001_create_users_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Users::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Users::Id)
                                .string()
                                .not_null()
                                .primary_key(),
                        )
                        .col(ColumnDef::new(Users::Username).string().not_null().unique_key())
                        .col(ColumnDef::new(Users::Email).string().not_null().unique_key())
                        .col(ColumnDef::new(Users::CreatedAt).timestamp_with_time_zone().not_null())
                        .col(ColumnDef::new(Users::UpdatedAt).timestamp_with_time_zone().not_null())
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(Users::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    pub enum Users {
        Table,
        Id,
        Username,
        Email,
        CreatedAt,
        UpdatedAt,
    }
}

mod m20220101_000002_create_categories_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Categories::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Categories::Id)
                                .string()
                                .not_null()
                                .primary_key(),
                        )
                        .col(ColumnDef::new(Categories::Name).string().not_null())
                        .col(ColumnDef::new(Categories::Description).string())
                        .col(ColumnDef::new(Categories::UserId).string().not_null())
                        .col(ColumnDef::new(Categories::CreatedAt).timestamp_with_time_zone().not_null())
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_categories_user_id")
                                .from(Categories::Table, Categories::UserId)
                                .to(m20220101_000001_create_users_table::Users::Table, m20220101_000001_create_users_table::Users::Id)
                                .on_delete(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(Categories::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    pub enum Categories {
        Table,
        Id,
        Name,
        Description,
        UserId,
        CreatedAt,
    }
}

mod m20220101_000003_create_reminders_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Reminders::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(Reminders::Id)
                                .string()
                                .not_null()
                                .primary_key(),
                        )
                        .col(ColumnDef::new(Reminders::Title).string().not_null())
                        .col(ColumnDef::new(Reminders::Description).string())
                        .col(ColumnDef::new(Reminders::ReminderType).string().not_null())
                        .col(ColumnDef::new(Reminders::UserId).string().not_null())
                        .col(ColumnDef::new(Reminders::CategoryId).string())
                        .col(ColumnDef::new(Reminders::RemindAt).timestamp_with_time_zone().not_null())
                        .col(ColumnDef::new(Reminders::ExpireAt).timestamp_with_time_zone())
                        .col(ColumnDef::new(Reminders::RecurrencePattern).string())
                        .col(ColumnDef::new(Reminders::CronExpression).string())
                        .col(ColumnDef::new(Reminders::IsActive).boolean().not_null().default(true))
                        .col(ColumnDef::new(Reminders::IsCompleted).boolean().not_null().default(false))
                        .col(ColumnDef::new(Reminders::CreatedAt).timestamp_with_time_zone().not_null())
                        .col(ColumnDef::new(Reminders::UpdatedAt).timestamp_with_time_zone().not_null())
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_reminders_user_id")
                                .from(Reminders::Table, Reminders::UserId)
                                .to(m20220101_000001_create_users_table::Users::Table, m20220101_000001_create_users_table::Users::Id)
                                .on_delete(ForeignKeyAction::Cascade),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_reminders_category_id")
                                .from(Reminders::Table, Reminders::CategoryId)
                                .to(m20220101_000002_create_categories_table::Categories::Table, m20220101_000002_create_categories_table::Categories::Id)
                                .on_delete(ForeignKeyAction::SetNull),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(Reminders::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    pub enum Reminders {
        Table,
        Id,
        Title,
        Description,
        ReminderType,
        UserId,
        CategoryId,
        RemindAt,
        ExpireAt,
        RecurrencePattern,
        CronExpression,
        IsActive,
        IsCompleted,
        CreatedAt,
        UpdatedAt,
    }
}

mod m20220101_000004_create_reminder_logs_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(ReminderLogs::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(ReminderLogs::Id)
                                .string()
                                .not_null()
                                .primary_key(),
                        )
                        .col(ColumnDef::new(ReminderLogs::ReminderId).string().not_null())
                        .col(ColumnDef::new(ReminderLogs::TriggeredAt).timestamp_with_time_zone().not_null())
                        .col(ColumnDef::new(ReminderLogs::Status).string().not_null())
                        .col(ColumnDef::new(ReminderLogs::Message).string())
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_reminder_logs_reminder_id")
                                .from(ReminderLogs::Table, ReminderLogs::ReminderId)
                                .to(m20220101_000003_create_reminders_table::Reminders::Table, m20220101_000003_create_reminders_table::Reminders::Id)
                                .on_delete(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(ReminderLogs::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    pub enum ReminderLogs {
        Table,
        Id,
        ReminderId,
        TriggeredAt,
        Status,
        Message,
    }
}
