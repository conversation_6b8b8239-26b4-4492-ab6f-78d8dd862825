# 到期提醒工具 API 测试文档

## 服务信息
- **Base URL**: `http://localhost:3000`
- **Content-Type**: `application/json`

## 1. 用户管理 API

### 1.1 创建用户
**POST** `/api/users`

**请求体：**
```json
{
  "username": "张三",
  "email": "<EMAIL>"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "张三",
    "email": "zhang<PERSON>@example.com",
    "created_at": "2025-08-27T03:26:00Z",
    "updated_at": "2025-08-27T03:26:00Z"
  },
  "message": null
}
```

### 1.2 获取所有用户
**GET** `/api/users`

**请求体：** 无

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "username": "张三",
      "email": "zhang<PERSON>@example.com",
      "created_at": "2025-08-27T03:26:00Z",
      "updated_at": "2025-08-27T03:26:00Z"
    }
  ],
  "message": null
}
```

### 1.3 获取单个用户
**GET** `/api/users/{user_id}`

**路径参数：**
- `user_id`: 用户ID (UUID格式)

**请求体：** 无

### 1.4 删除用户
**DELETE** `/api/users/{user_id}`

**路径参数：**
- `user_id`: 用户ID (UUID格式)

**请求体：** 无

**响应：** 204 No Content

---

## 2. 分类管理 API

### 2.1 创建分类
**POST** `/api/users/{user_id}/categories`

**路径参数：**
- `user_id`: 用户ID (UUID格式)

**请求体：**
```json
{
  "name": "医疗健康",
  "description": "医疗相关的提醒"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "660e8400-e29b-41d4-a716-************",
    "name": "医疗健康",
    "description": "医疗相关的提醒",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "created_at": "2025-08-27T03:26:00Z"
  },
  "message": null
}
```

### 2.2 获取用户的所有分类
**GET** `/api/users/{user_id}/categories`

**路径参数：**
- `user_id`: 用户ID (UUID格式)

**请求体：** 无

### 2.3 获取单个分类
**GET** `/api/users/{user_id}/categories/{category_id}`

**路径参数：**
- `user_id`: 用户ID (UUID格式)
- `category_id`: 分类ID (UUID格式)

### 2.4 删除分类
**DELETE** `/api/users/{user_id}/categories/{category_id}`

**路径参数：**
- `user_id`: 用户ID (UUID格式)
- `category_id`: 分类ID (UUID格式)

**响应：** 204 No Content

---

## 3. 提醒管理 API

### 3.1 创建定期提醒（每日吃药）
**POST** `/api/users/{user_id}/reminders`

**路径参数：**
- `user_id`: 用户ID (UUID格式)

**请求体：**
```json
{
  "title": "早餐后服药",
  "description": "每天早餐后服用降压药",
  "reminder_type": "Recurring",
  "category_id": "660e8400-e29b-41d4-a716-************",
  "remind_at": "2025-08-27T08:00:00Z",
  "expire_at": null,
  "recurrence_pattern": "Daily",
  "cron_expression": null
}
```

### 3.2 创建一次性提醒（食品到期）
**POST** `/api/users/{user_id}/reminders`

**路径参数：**
- `user_id`: 用户ID (UUID格式)

**请求体：**
```json
{
  "title": "牛奶即将过期",
  "description": "冰箱里的牛奶将在明天过期",
  "reminder_type": "OneTime",
  "category_id": "660e8400-e29b-41d4-a716-************",
  "remind_at": "2025-08-28T18:00:00Z",
  "expire_at": "2025-08-29T00:00:00Z",
  "recurrence_pattern": null,
  "cron_expression": null
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "770e8400-e29b-41d4-a716-************",
    "title": "早餐后服药",
    "description": "每天早餐后服用降压药",
    "reminder_type": "Recurring",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "category_id": "660e8400-e29b-41d4-a716-************",
    "remind_at": "2025-08-27T08:00:00Z",
    "expire_at": null,
    "recurrence_pattern": "Daily",
    "cron_expression": null,
    "is_active": true,
    "is_completed": false,
    "created_at": "2025-08-27T03:26:00Z",
    "updated_at": "2025-08-27T03:26:00Z"
  },
  "message": null
}
```

### 3.3 获取用户的所有提醒
**GET** `/api/users/{user_id}/reminders`

**路径参数：**
- `user_id`: 用户ID (UUID格式)

**查询参数（可选）：**
- `category_id`: 分类ID筛选
- `is_active`: 是否激活 (true/false)
- `reminder_type`: 提醒类型 ("OneTime"/"Recurring")

**示例：**
```
GET /api/users/{user_id}/reminders?is_active=true&reminder_type=Recurring
```

### 3.4 获取单个提醒
**GET** `/api/users/{user_id}/reminders/{reminder_id}`

**路径参数：**
- `user_id`: 用户ID (UUID格式)
- `reminder_id`: 提醒ID (UUID格式)

### 3.5 更新提醒
**PUT** `/api/users/{user_id}/reminders/{reminder_id}`

**路径参数：**
- `user_id`: 用户ID (UUID格式)
- `reminder_id`: 提醒ID (UUID格式)

**请求体：**
```json
{
  "title": "晚餐后服药",
  "description": "改为晚餐后服用",
  "remind_at": "2025-08-27T19:00:00Z",
  "is_active": true,
  "is_completed": false
}
```

### 3.6 删除提醒
**DELETE** `/api/users/{user_id}/reminders/{reminder_id}`

**路径参数：**
- `user_id`: 用户ID (UUID格式)
- `reminder_id`: 提醒ID (UUID格式)

**响应：** 204 No Content

---

## 4. 枚举值说明

### 4.1 提醒类型 (reminder_type)
- `"OneTime"`: 一次性提醒
- `"Recurring"`: 定期提醒

### 4.2 重复模式 (recurrence_pattern)
- `"Daily"`: 每天
- `"Weekly"`: 每周
- `"Monthly"`: 每月
- `"Yearly"`: 每年
- `"Custom"`: 自定义（需要cron表达式）

---

## 5. Apifox 导入配置

### 5.1 环境变量设置
在Apifox中创建环境，设置以下变量：
- `baseUrl`: `http://localhost:3000`
- `userId`: `550e8400-e29b-41d4-a716-************` (示例用户ID)
- `categoryId`: `660e8400-e29b-41d4-a716-************` (示例分类ID)

### 5.2 公共Headers
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

---

## 6. 测试用例推荐

### 6.1 完整工作流测试
1. **创建用户** → 获得 `user_id`
2. **创建分类** → 获得 `category_id`
3. **创建定期提醒** → 测试每日吃药场景
4. **创建一次性提醒** → 测试食品到期场景
5. **查询提醒列表** → 验证数据完整性
6. **更新提醒** → 测试状态修改
7. **删除提醒** → 测试清理功能

### 6.2 边界条件测试
- 无效的UUID格式
- 不存在的用户/分类/提醒ID
- 无效的时间格式
- 无效的枚举值

### 6.3 业务逻辑测试
- 过期时间提醒测试（设置1分钟后的提醒时间）
- 重复模式验证
- 激活/停用状态切换

---

## 7. 错误响应格式

### 7.1 404 Not Found
```json
{
  "success": false,
  "data": null,
  "message": "Resource not found"
}
```

### 7.2 400 Bad Request
```json
{
  "success": false,
  "data": null,
  "message": "Invalid request data"
}
```

### 7.3 500 Internal Server Error
```json
{
  "success": false,
  "data": null,
  "message": "Internal server error"
}
```

---

## 8. 时间格式说明

所有时间字段使用 **ISO 8601** 格式，UTC时区：
- 格式：`YYYY-MM-DDTHH:mm:ssZ`
- 示例：`2025-08-27T08:00:00Z`
- 注意：必须包含末尾的 `Z` 表示UTC时区

---

## 9. 快速开始

### 9.1 启动服务
```bash
cd backend
cargo run
```

### 9.2 快速测试
使用curl命令快速测试：

```bash
# 创建用户
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{"username": "测试用户", "email": "<EMAIL>"}'

# 创建提醒
curl -X POST http://localhost:3000/api/users/{user_id}/reminders \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试提醒",
    "description": "这是一个测试提醒",
    "reminder_type": "OneTime",
    "remind_at": "2025-08-27T12:00:00Z"
  }'
```

记得将 `{user_id}` 替换为实际的用户ID。
