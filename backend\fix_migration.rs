// 迁移冲突修复工具
// 运行方式: cargo run --bin fix_migration

use sea_orm::{Database, DatabaseConnection, DbErr, Statement};
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载环境变量
    dotenvy::dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL 环境变量必须设置");
    
    println!("🔍 连接数据库检查迁移状态...");
    let db = Database::connect(&database_url).await?;
    
    // 直接尝试清理冲突记录
    println!("🔧 尝试清理冲突的迁移记录...");
    match clear_conflicting_migration(&db).await {
        Ok(_) => {
            println!("✅ 冲突迁移记录已清理");
            run_migrations(&db).await?;
        }
        Err(e) => {
            println!("清理失败，尝试检查迁移表状态: {}", e);
            check_migration_table(&db).await?;
            
            // 如果还是失败，提供完全重置选项
            println!("\n🔄 尝试完全重置迁移表...");
            match reset_migration_table(&db).await {
                Ok(_) => {
                    println!("✅ 迁移表已重置");
                    run_migrations(&db).await?;
                }
                Err(e) => {
                    println!("❌ 重置失败: {}", e);
                    println!("请手动处理或删除数据库重新创建");
                }
            }
        }
    }
    
    Ok(())
}

async fn check_migration_table(db: &DatabaseConnection) -> Result<(), DbErr> {
    use sea_orm::*;
    
    println!("检查 seaql_migrations 表...");
    
    // 查询迁移表
    let result = db
        .query_all(Statement::from_string(
            db.get_database_backend(),
            "SELECT version, applied_at FROM seaql_migrations ORDER BY applied_at".to_string(),
        ))
        .await;
    
    match result {
        Ok(rows) => {
            println!("当前迁移记录:");
            for row in rows {
                let version: String = row.try_get("", "version")?;
                let applied_at: Option<chrono::DateTime<chrono::Utc>> = row.try_get("", "applied_at").ok();
                println!("  - 版本: {}, 应用时间: {:?}", version, applied_at);
            }
        }
        Err(e) => {
            println!("迁移表不存在或查询失败: {}", e);
        }
    }
    
    Ok(())
}

async fn clear_conflicting_migration(db: &DatabaseConnection) -> Result<(), DbErr> {
    use sea_orm::*;
    
    println!("清理冲突的迁移记录...");
    
    // 删除版本为 'migrator' 的记录
    let result = db
        .execute(Statement::from_string(
            db.get_database_backend(),
            "DELETE FROM seaql_migrations WHERE version = 'migrator'".to_string(),
        ))
        .await?;
    
    println!("删除了 {} 条冲突记录", result.rows_affected());
    
    Ok(())
}

async fn reset_migration_table(db: &DatabaseConnection) -> Result<(), DbErr> {
    use sea_orm::*;
    
    println!("重置迁移表...");
    
    // 删除整个迁移表
    let result = db
        .execute(Statement::from_string(
            db.get_database_backend(),
            "DROP TABLE IF EXISTS seaql_migrations CASCADE".to_string(),
        ))
        .await?;
    
    println!("迁移表已删除");
    
    Ok(())
}

async fn run_migrations(_db: &DatabaseConnection) -> Result<(), DbErr> {
    // 由于模块结构问题，我们不在这里直接运行迁移
    println!("✅ 冲突已清理，请重新运行主应用:");
    println!("   cargo run --bin backend");
    println!("\n主应用将自动运行迁移");
    
    Ok(())
}
