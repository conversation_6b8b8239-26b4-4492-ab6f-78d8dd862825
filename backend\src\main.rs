mod database;
mod entities;
mod handlers;
mod migrator;
mod models;
mod scheduler;

use axum::{
    http::Method,
    routing::{delete, get, post, put},
    Router,
};
use std::env;
use tower::ServiceBuilder;
use tower_http::cors::{Any, CorsLayer};

use crate::{
    database::create_connection,
    handlers::*,
    scheduler::ReminderScheduler,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    // 加载环境变量
    dotenvy::dotenv().ok();
    
    // 创建数据库连接
    let db = create_connection().await?;
    
    // 创建并启动调度器
    let scheduler = ReminderScheduler::new(db.clone()).await?;
    scheduler.start().await?;
    
    // 设置CORS
    let cors = CorsLayer::new()
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers(Any)
        .allow_origin(Any);
    
    // 构建路由
    let app = Router::new()
        // 用户路由
        .route("/api/users", post(create_user))
        .route("/api/users", get(get_users))
        .route("/api/users/:user_id", get(get_user))
        .route("/api/users/:user_id", delete(delete_user))
        
        // 分类路由
        .route("/api/users/:user_id/categories", post(create_category))
        .route("/api/users/:user_id/categories", get(get_categories))
        .route("/api/users/:user_id/categories/:category_id", get(get_category))
        .route("/api/users/:user_id/categories/:category_id", delete(delete_category))
        
        // 提醒路由
        .route("/api/users/:user_id/reminders", post(create_reminder))
        .route("/api/users/:user_id/reminders", get(get_reminders))
        .route("/api/users/:user_id/reminders/:reminder_id", get(get_reminder))
        .route("/api/users/:user_id/reminders/:reminder_id", put(update_reminder))
        .route("/api/users/:user_id/reminders/:reminder_id", delete(delete_reminder))
        
        .layer(ServiceBuilder::new().layer(cors))
        .with_state(db);
    
    // 启动服务器
    let port = env::var("PORT").unwrap_or_else(|_| "3000".to_string());
    let addr = format!("0.0.0.0:{}", port);
    
    tracing::info!("🚀 服务器启动在 {}", addr);
    
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app).await?;
    
    Ok(())
}
