use sea_orm::{Database, DatabaseConnection, DbErr};
use sea_orm_migration::MigratorTrait;
use std::env;

use crate::migrator::Migrator;

pub type DbConn = DatabaseConnection;

pub async fn create_connection() -> Result<DbConn, DbErr> {
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL 环境变量必须设置");
    
    tracing::info!("连接数据库: {}", database_url.split('@').last().unwrap_or("隐藏连接信息"));
    
    let db = Database::connect(&database_url).await?;
    
    tracing::info!("数据库连接成功，开始运行迁移...");
    
    // 运行数据库迁移，使用更安全的方式
    match Migrator::up(&db, None).await {
        Ok(_) => {
            tracing::info!("数据库迁移完成");
        }
        Err(e) => {
            // 如果迁移失败，检查是否是重复键错误
            let error_msg = e.to_string();
            if error_msg.contains("duplicate key value violates unique constraint") 
                && error_msg.contains("seaql_migrations_pkey") {
                tracing::warn!("迁移表已存在，跳过迁移: {}", e);
                // 继续使用现有连接
            } else {
                tracing::error!("数据库迁移失败: {}", e);
                return Err(e);
            }
        }
    }
    
    Ok(db)
}
